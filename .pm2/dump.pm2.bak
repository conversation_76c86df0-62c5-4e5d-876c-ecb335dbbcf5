[{"kill_retry_time": 100, "windowsHide": true, "username": "app", "treekill": true, "automation": true, "pmx": true, "instance_var": "NODE_APP_INSTANCE", "watch": false, "autorestart": true, "vizion": true, "merge_logs": true, "env": {"PM2_USAGE": "CLI", "_": "/usr/local/bin/pm2", "SSH_TTY": "/dev/pts/0", "MAIL": "/var/mail/app", "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1003/bus", "PATH": "/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games", "SSH_CLIENT": "*********** 57285 22", "XDG_RUNTIME_DIR": "/run/user/1003", "XDG_SESSION_ID": "3", "SHLVL": "1", "USER": "app", "TERM": "xterm", "XDG_SESSION_CLASS": "user", "SSH_CONNECTION": "*********** 57285 ************* 22", "LANG": "de_CH.UTF-8", "HOME": "/app", "XDG_SESSION_TYPE": "tty", "LOGNAME": "app", "PWD": "/app", "LANGUAGE": "de_CH:de", "SHELL": "/bin/bash", "PM2_HOME": "/app/.pm2", "server": {}, "unique_id": "cf610e19-ec87-4437-b083-2d0b497315ac"}, "namespace": "default", "filter_env": [], "name": "server", "node_args": [], "pm_exec_path": "/app/server.js", "pm_cwd": "/app", "exec_interpreter": "node", "exec_mode": "fork_mode", "pm_out_log_path": "/app/.pm2/logs/server-out.log", "pm_err_log_path": "/app/.pm2/logs/server-error.log", "pm_pid_path": "/app/.pm2/pids/server-0.pid", "km_link": false, "vizion_running": false, "NODE_APP_INSTANCE": 0, "PM2_USAGE": "CLI", "_": "/usr/local/bin/pm2", "SSH_TTY": "/dev/pts/0", "MAIL": "/var/mail/app", "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1003/bus", "PATH": "/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games", "SSH_CLIENT": "*********** 57285 22", "XDG_RUNTIME_DIR": "/run/user/1003", "XDG_SESSION_ID": "3", "SHLVL": "1", "USER": "app", "TERM": "xterm", "XDG_SESSION_CLASS": "user", "SSH_CONNECTION": "*********** 57285 ************* 22", "LANG": "de_CH.UTF-8", "HOME": "/app", "XDG_SESSION_TYPE": "tty", "LOGNAME": "app", "PWD": "/app", "LANGUAGE": "de_CH:de", "SHELL": "/bin/bash", "PM2_HOME": "/app/.pm2", "unique_id": "cf610e19-ec87-4437-b083-2d0b497315ac", "status": "online", "pm_uptime": 1717160409437, "axm_actions": [{"action_name": "km:heapdump", "action_type": "internal", "arity": 2}, {"action_name": "km:cpu:profiling:start", "action_type": "internal", "arity": 2}, {"action_name": "km:cpu:profiling:stop", "action_type": "internal", "arity": 1}, {"action_name": "km:heap:sampling:start", "action_type": "internal", "arity": 2}, {"action_name": "km:heap:sampling:stop", "action_type": "internal", "arity": 1}], "axm_monitor": {"Heap Size": {"value": "18.51", "type": "internal/v8/heap/total", "unit": "MiB", "historic": true}, "Heap Usage": {"value": 80.14, "type": "internal/v8/heap/usage", "unit": "%", "historic": true}, "Used Heap Size": {"value": "14.83", "type": "internal/v8/heap/used", "unit": "MiB", "historic": true}, "Active requests": {"value": 0, "type": "internal/libuv/requests", "historic": true}, "Active handles": {"value": 5, "type": "internal/libuv/handles", "historic": true}, "Event Loop Latency": {"value": "0.21", "type": "internal/libuv/latency/p50", "unit": "ms", "historic": true}, "Event Loop Latency p95": {"value": "1.11", "type": "internal/libuv/latency/p95", "unit": "ms", "historic": true}}, "axm_options": {"error": true, "heapdump": true, "feature.profiler.heapsnapshot": true, "feature.profiler.heapsampling": true, "feature.profiler.cpu_js": true, "latency": true, "catchExceptions": true, "profiling": true, "metrics": {"http": true, "runtime": true, "eventLoop": true, "network": false, "v8": true}, "standalone": false, "tracing": {"outbound": false, "enabled": false}, "module_conf": {}, "apm": {"version": "5.0.0", "type": "node"}, "module_name": "server", "module_version": "5.1.2"}, "axm_dynamic": {}, "created_at": 1717160409327, "restart_time": 75, "unstable_restarts": 0, "version": "1.0.0", "versioning": {"type": "git", "revision": "31508634aa09719e203d018b3cc1a5aa85efac76", "comment": "initial\n", "unstaged": true, "branch": "master", "remotes": [], "branch_exists_on_remote": false, "ahead": false, "next_rev": null, "prev_rev": null, "update_time": "2024-05-31T13:00:09.447Z", "repo_path": "/app"}, "node_version": "10.24.0", "exit_code": 0}]