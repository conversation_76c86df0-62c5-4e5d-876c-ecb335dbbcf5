[{"kill_retry_time": 100, "windowsHide": true, "username": "app", "treekill": true, "automation": true, "pmx": true, "instance_var": "NODE_APP_INSTANCE", "watch": false, "autorestart": true, "vizion": true, "merge_logs": true, "env": {"PM2_USAGE": "CLI", "_": "/usr/local/bin/pm2", "SSH_TTY": "/dev/pts/0", "MAIL": "/var/mail/app", "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1003/bus", "PATH": "/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games", "SSH_CLIENT": "*********** 57285 22", "XDG_RUNTIME_DIR": "/run/user/1003", "XDG_SESSION_ID": "3", "SHLVL": "1", "USER": "app", "TERM": "xterm", "XDG_SESSION_CLASS": "user", "SSH_CONNECTION": "*********** 57285 ************* 22", "LANG": "de_CH.UTF-8", "HOME": "/app", "XDG_SESSION_TYPE": "tty", "LOGNAME": "app", "PWD": "/app", "LANGUAGE": "de_CH:de", "SHELL": "/bin/bash", "PM2_HOME": "/app/.pm2", "server": {}, "unique_id": "cf610e19-ec87-4437-b083-2d0b497315ac"}, "namespace": "default", "filter_env": [], "name": "server", "node_args": [], "pm_exec_path": "/app/server.js", "pm_cwd": "/app", "exec_interpreter": "node", "exec_mode": "fork_mode", "pm_out_log_path": "/app/.pm2/logs/server-out.log", "pm_err_log_path": "/app/.pm2/logs/server-error.log", "pm_pid_path": "/app/.pm2/pids/server-0.pid", "km_link": false, "vizion_running": false, "NODE_APP_INSTANCE": 0, "PM2_USAGE": "CLI", "_": "/usr/local/bin/pm2", "SSH_TTY": "/dev/pts/0", "MAIL": "/var/mail/app", "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1003/bus", "PATH": "/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games", "SSH_CLIENT": "*********** 57285 22", "XDG_RUNTIME_DIR": "/run/user/1003", "XDG_SESSION_ID": "3", "SHLVL": "1", "USER": "app", "TERM": "xterm", "XDG_SESSION_CLASS": "user", "SSH_CONNECTION": "*********** 57285 ************* 22", "LANG": "de_CH.UTF-8", "HOME": "/app", "XDG_SESSION_TYPE": "tty", "LOGNAME": "app", "PWD": "/app", "LANGUAGE": "de_CH:de", "SHELL": "/bin/bash", "PM2_HOME": "/app/.pm2", "unique_id": "cf610e19-ec87-4437-b083-2d0b497315ac", "status": "online", "pm_uptime": 1726630471655, "axm_actions": [{"arity": 2, "action_type": "internal", "action_name": "km:heapdump"}, {"arity": 2, "action_type": "internal", "action_name": "km:cpu:profiling:start"}, {"arity": 1, "action_type": "internal", "action_name": "km:cpu:profiling:stop"}, {"arity": 2, "action_type": "internal", "action_name": "km:heap:sampling:start"}, {"arity": 1, "action_type": "internal", "action_name": "km:heap:sampling:stop"}], "axm_monitor": {"Event Loop Latency p95": {"historic": true, "unit": "ms", "type": "internal/libuv/latency/p95", "value": "2.00"}, "Event Loop Latency": {"historic": true, "unit": "ms", "type": "internal/libuv/latency/p50", "value": "0.64"}, "Active handles": {"historic": true, "type": "internal/libuv/handles", "value": 5}, "Active requests": {"historic": true, "type": "internal/libuv/requests", "value": 0}, "Used Heap Size": {"historic": true, "unit": "MiB", "type": "internal/v8/heap/used", "value": "22.79"}, "Heap Usage": {"historic": true, "unit": "%", "type": "internal/v8/heap/usage", "value": 77.58}, "Heap Size": {"historic": true, "unit": "MiB", "type": "internal/v8/heap/total", "value": "29.37"}}, "axm_options": {"module_version": "5.1.2", "module_name": "server", "apm": {"type": "node", "version": "5.0.0"}, "module_conf": {}, "tracing": {"enabled": false, "outbound": false}, "standalone": false, "metrics": {"v8": true, "network": false, "eventLoop": true, "runtime": true, "http": true}, "profiling": true, "catchExceptions": true, "latency": true, "feature.profiler.cpu_js": true, "feature.profiler.heapsampling": true, "feature.profiler.heapsnapshot": true, "heapdump": true, "error": true}, "axm_dynamic": {}, "created_at": 1726630471383, "restart_time": 194, "unstable_restarts": 0, "version": "1.0.0", "versioning": {"repo_path": "/app", "update_time": "2024-09-18T03:34:31.819Z", "prev_rev": null, "next_rev": null, "ahead": false, "branch_exists_on_remote": false, "remotes": [], "branch": "master", "unstaged": true, "comment": "initial\n", "revision": "31508634aa09719e203d018b3cc1a5aa85efac76", "type": "git"}, "node_version": "10.24.0", "prev_restart_delay": 0, "exit_code": 0}]