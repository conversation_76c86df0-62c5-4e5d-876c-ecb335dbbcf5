  var fs = require('fs');

function formatDate(date) {
  var hours = date.getHours();
  var minutes = date.getMinutes();
  var ampm = hours >= 12 ? 'pm' : 'am';
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  minutes = minutes < 10 ? '0'+minutes : minutes;
  var strTime = hours + ':' + minutes + ' ' + ampm;
  return date.getDate() + "." + (date.getMonth() + 1) +"." + date.getFullYear() + "  " + strTime;
}


function walkSync(currentDirPath, callback) {
  var fs = require('fs'),
    path = require('path');
  fs.readdirSync(currentDirPath).forEach(function(name) {
    var filePath = path.join(currentDirPath, name);
    var stat = fs.statSync(filePath);
    if (stat.isFile()) {
      callback(filePath, stat);
    } else if (stat.isDirectory()) {
      walkSync(filePath, callback);
    }
  });
}

walkSync("/root/Tresors", function(item) { 
console.log(item);
      let stat = fs.statSync(item);
	console.log(stat.mtime);
        console.log(formatDate(stat.mtime));
});
