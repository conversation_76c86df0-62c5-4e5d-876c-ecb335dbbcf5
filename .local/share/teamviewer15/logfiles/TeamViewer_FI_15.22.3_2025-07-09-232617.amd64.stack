/opt/teamviewer/tv_bin/TeamViewer[0xd00573]
/opt/teamviewer/tv_bin/TeamViewer[0xcfef7c]
/opt/teamviewer/tv_bin/TeamViewer[0xd18a2e]
/lib/x86_64-linux-gnu/libpthread.so.0(+0x12730)[0x7ff0c8881730]
/lib/x86_64-linux-gnu/libc.so.6(gsignal+0x10b)[0x7ff0c85618eb]
/lib/x86_64-linux-gnu/libc.so.6(abort+0x121)[0x7ff0c854c535]
/opt/teamviewer/tv_bin/TeamViewer[0x137967b]
/opt/teamviewer/tv_bin/TeamViewer[0x1377db0]
/opt/teamviewer/tv_bin/TeamViewer[0x1377ddb]
/opt/teamviewer/tv_bin/TeamViewer[0x894250]
/opt/teamviewer/tv_bin/TeamViewer[0x13466da]
/opt/teamviewer/tv_bin/TeamViewer[0x13466f4]
/opt/teamviewer/tv_bin/TeamViewer[0x134672d]
/opt/teamviewer/tv_bin/TeamViewer[0x1346749]
/lib/x86_64-linux-gnu/libc.so.6(+0x39ebc)[0x7ff0c8563ebc]
/lib/x86_64-linux-gnu/libc.so.6(+0x39fea)[0x7ff0c8563fea]
/lib/x86_64-linux-gnu/libX11.so.6(_XDefaultIOError+0x85)[0x7ff0c6043195]
/opt/teamviewer/tv_bin/RTlib/qt/plugins/platforms/../../lib/libQt5XcbQpa.so.5(+0x56bd2)[0x7ff0c01acbd2]
/lib/x86_64-linux-gnu/libX11.so.6(_XIOError+0x4e)[0x7ff0c60433ce]
/lib/x86_64-linux-gnu/libX11.so.6(+0x4455c)[0x7ff0c604155c]
/lib/x86_64-linux-gnu/libX11.so.6(XSync+0x4d)[0x7ff0c603cbad]
/lib/x86_64-linux-gnu/libX11.so.6(XCloseDisplay+0x77)[0x7ff0c601dbd7]
/opt/teamviewer/tv_bin/TeamViewer[0x4af9d4]
/opt/teamviewer/tv_bin/TeamViewer[0xd43724]
/opt/teamviewer/tv_bin/TeamViewer[0xd43763]
/opt/teamviewer/tv_bin/TeamViewer[0xc54c9b]
/opt/teamviewer/tv_bin/TeamViewer[0xc54cb9]
/opt/teamviewer/tv_bin/TeamViewer[0x4af9d4]
/opt/teamviewer/tv_bin/TeamViewer[0x4c0ad3]
/opt/teamviewer/tv_bin/TeamViewer[0x4b5ba1]
/opt/teamviewer/tv_bin/TeamViewer[0x4c9a4a]
/opt/teamviewer/tv_bin/TeamViewer[0x42514c]
/lib/x86_64-linux-gnu/libc.so.6(__libc_start_main+0xeb)[0x7ff0c854e09b]
/opt/teamviewer/tv_bin/TeamViewer[0x4af19b]
