/opt/teamviewer/tv_bin/TeamViewer[0xd00573]
/opt/teamviewer/tv_bin/TeamViewer[0xcfef7c]
/opt/teamviewer/tv_bin/TeamViewer[0xd18a2e]
/lib/x86_64-linux-gnu/libpthread.so.0(+0x12730)[0x7fe571728730]
/lib/x86_64-linux-gnu/libc.so.6(gsignal+0x10b)[0x7fe5714088eb]
/lib/x86_64-linux-gnu/libc.so.6(abort+0x121)[0x7fe5713f3535]
/opt/teamviewer/tv_bin/TeamViewer[0x137967b]
/opt/teamviewer/tv_bin/TeamViewer[0x1377db0]
/opt/teamviewer/tv_bin/TeamViewer[0x1377ddb]
/opt/teamviewer/tv_bin/TeamViewer[0x894250]
/opt/teamviewer/tv_bin/TeamViewer[0x13466da]
/opt/teamviewer/tv_bin/TeamViewer[0x13466f4]
/opt/teamviewer/tv_bin/TeamViewer[0x134672d]
/opt/teamviewer/tv_bin/TeamViewer[0x1346749]
/lib/x86_64-linux-gnu/libc.so.6(+0x39ebc)[0x7fe57140aebc]
/lib/x86_64-linux-gnu/libc.so.6(+0x39fea)[0x7fe57140afea]
/lib/x86_64-linux-gnu/libX11.so.6(_XDefaultIOError+0x85)[0x7fe56eeea195]
/opt/teamviewer/tv_bin/RTlib/qt/plugins/platforms/../../lib/libQt5XcbQpa.so.5(+0x56bd2)[0x7fe56c059bd2]
/lib/x86_64-linux-gnu/libX11.so.6(_XIOError+0x4e)[0x7fe56eeea3ce]
/lib/x86_64-linux-gnu/libX11.so.6(+0x43728)[0x7fe56eee7728]
/lib/x86_64-linux-gnu/libX11.so.6(_XFlush+0x35)[0x7fe56eee7f15]
/lib/x86_64-linux-gnu/libX11.so.6(_XGetRequest+0x55)[0x7fe56eeeaa05]
/lib/x86_64-linux-gnu/libGLX_mesa.so.0(+0x350d2)[0x7fe5525370d2]
/opt/teamviewer/tv_bin/RTlib/qt/plugins/xcbglintegrations/libqxcb-glx-integration.so(+0xb381)[0x7fe5525a3381]
/opt/teamviewer/tv_bin/RTlib/qt/plugins/xcbglintegrations/libqxcb-glx-integration.so(+0xb39d)[0x7fe5525a339d]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Gui.so.5(_ZN14QOpenGLContext7destroyEv+0x71)[0x7fe577cfccc9]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Gui.so.5(_ZN14QOpenGLContextD1Ev+0x17)[0x7fe577cfcf3d]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Gui.so.5(_ZN14QOpenGLContextD0Ev+0x9)[0x7fe577cfcf4f]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Quick.so.5(+0x1b785b)[0x7fe578eac85b]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Quick.so.5(+0x1bb6c1)[0x7fe578eb06c1]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Quick.so.5(+0x1bafb0)[0x7fe578eaffb0]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Quick.so.5(+0x1bb2b3)[0x7fe578eb02b3]
/opt/teamviewer/tv_bin/RTlib/qt/lib/libQt5Core.so.5(+0xbdab2)[0x7fe57752aab2]
/lib/x86_64-linux-gnu/libpthread.so.0(+0x7fa3)[0x7fe57171dfa3]
/lib/x86_64-linux-gnu/libc.so.6(clone+0x3f)[0x7fe5714ca06f]
