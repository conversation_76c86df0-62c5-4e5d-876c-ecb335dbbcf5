[{"Log Stream Opened": "2025-07-01 09:32:47", "Email": "<EMAIL>", "Version": "Tresorit/3.5.1266.4580 (Linux-Desktop-x64-Release; 0000000000000000000000000000000000000000)", "Application": "cli"}, ["2025-07-01 09:32:47", "Error", ["BackupLog", "main.cpp", 266, {}, [["connect() failed, error: Connection refused (errno=111)", "unix_ipc.cpp", 298, {}, [], {}]], {}]], ["2025-07-01 09:32:47", "Error", ["BackupLog", "main.cpp", 206, {}, [["CoreLog", "main.cpp", 255, {}, [["CLIDaemonNotRunning", "async_tresorit.hpp", 242, {}, [["CLIDaemonNotRunning", "async_tresorit.hpp", 193, {}, [], {}]], {}]], {}]], {}]], ["2025-07-01 09:32:47", "Error", ["BackupLog", "main.cpp", 206, {}, [["CoreLog", "main.cpp", 256, {}, [["CLICommand", "main.cpp", 246, {"help": "false", "ipc_mode": "false", "order-by": "false", "please": "false", "porcelain": "true", "tresors": "true", "user": "false"}, [], {}]], {}]], {}]], {"Log Stream Closed": "2025-07-01 09:32:47"}]