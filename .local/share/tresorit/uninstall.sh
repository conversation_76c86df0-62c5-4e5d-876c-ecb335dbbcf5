#!/bin/sh
THIS_DIR="$(cd "$(dirname "$0")" && pwd)"
if test "$(whoami)" = "root"; then
    echo "We've detected, that you are trying to uninstall Tresorit as root."
    echo "If you'd like to continue as root, please keep in mind that the installer is going to try to delete the $HOME/.local/share/applications/tresorit.desktop file."
    echo "Please continue only if you are sure you'd like to do this."
fi
printf "Are you sure you want to uninstall Tresorit from your computer? [y/N] "
read CONTINUE
if test "$CONTINUE" = "y" -o "$CONTINUE" = "ye" -o "$CONTINUE" = "yes" -o "$CONTINUE" = "yep" -o "$CONTINUE" = "Y" -o "$CONTINUE" = "YE" -o "$CONTINUE" = "YES" -o "$CONTINUE" = "YEP"; then
    if test -f "$THIS_DIR/running.pid"; then
        echo "Tresorit is running on your computer. Please stop Tresorit before you continue."
    fi
    printf "Do you want to remove every Tresorit-related files from your computer? (If you answer no to this question, the uninstaller will skip deleting your local settings.) [Y/n] "
    read DEL_ALL

    killall tresorit
    killall tresorit-daemon

    rm -f "$HOME/.local/share/applications/tresorit.desktop"
    if test "$DEL_ALL" = "n" -o "$DEL_ALL" = "no" -o "$DEL_ALL" = "N" -o "$DEL_ALL" = "NO"; then
        rm -f "$THIS_DIR/tresorit"
        rm -f "$THIS_DIR/tresorit-daemon"
        rm -f "$THIS_DIR/tresorit-cli"
        rm -f "$THIS_DIR/tresorit.config"
        rm -f "$THIS_DIR/libTresorit.so"
        rm -f "$THIS_DIR/tresorit.png"
        rm -f "$THIS_DIR/uninstall.sh"
        rm -f "$THIS_DIR/running.pid"
        rm -f "$THIS_DIR/LICENSES.txt"
        rm -f "$THIS_DIR/UpdaterWatchdog"
        rm -rf "$THIS_DIR/QtQuick"
        rm -rf "$THIS_DIR/Reports"
        rm -rf "$THIS_DIR/QtQuick.2"
        rm -rf "$THIS_DIR/fonts"
    else
        rm -f "$THIS_DIR/tresorit"
        rm -f "$THIS_DIR/tresorit-daemon"
        rm -f "$THIS_DIR/tresorit-cli"
        rm -f "$THIS_DIR/tresorit.config"
        rm -f "$THIS_DIR/libTresorit.so"
        rm -f "$THIS_DIR/tresorit.png"
        rm -f "$THIS_DIR/uninstall.sh"
        rm -f "$THIS_DIR/running.pid"
        rm -f "$THIS_DIR/LICENSES.txt"
        rm -f "$THIS_DIR/UpdaterWatchdog"
        rm -rf "$THIS_DIR/QtQuick"
        rm -rf "$THIS_DIR/QtQuick.2"
        rm -rf "$THIS_DIR/fonts"
        rm -rf "$THIS_DIR/Logs"
        rm -rf "$THIS_DIR/Reports"
        rm -rf "$THIS_DIR/Temp"
        rm -rf "$THIS_DIR/Profiles"
    fi
    echo "Tresorit was uninstalled successfully!"
else
    echo "Uninstall canceled."
fi
exit 0

