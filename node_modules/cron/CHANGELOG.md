## [v1.7.1] - 2019-04-26
- GH-416 - Fix issue where next execution time is incorrect in some cases in Naoya Inada <<EMAIL>> in c08522ff80b3987843e9930c307b76d5fe38b5dc

## [v1.7.0] - 2019-03-19
- GH-408 - DST issue by <PERSON><PERSON> <<EMAIL>> in 1e971fd6dfa6ba4b0469d99dd64e6c31189d17d3 and 849a2467d16216a9dfa818c57cc26be6b6d0899b

## [v1.6.0] - 2018-11-15
- GH-393, GH-394 - Remove hard limit on max iters in favor of a timeout by <PERSON> <<EMAIL>> in 57632b0c06c56e82f40b740b8d7986be43842735
- GH-390 - better handling of real dates which are in the past by <PERSON> <<EMAIL>> in 7cbcc984aea6ec063e38829f68eb9bc0dfb1c775
