{"_args": [["cron@1.7.2", "/app"]], "_from": "cron@1.7.2", "_id": "cron@1.7.2", "_inBundle": false, "_integrity": "sha512-+SaJ2OfeRvfQqwXQ2kgr0Y5pzBR/lijf5OpnnaruwWnmI799JfWr2jN2ItOV9s3A/+TFOt6mxvKzQq5F0Jp6VQ==", "_location": "/cron", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cron@1.7.2", "name": "cron", "escapedName": "cron", "rawSpec": "1.7.2", "saveSpec": null, "fetchSpec": "1.7.2"}, "_requiredBy": ["/agenda"], "_resolved": "https://registry.npmjs.org/cron/-/cron-1.7.2.tgz", "_spec": "1.7.2", "_where": "/app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/ncb000gt"}, "bugs": {"url": "http://github.com/kelektiv/node-cron/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/toots"}, {"name": "<PERSON>", "url": "https://github.com/james<PERSON><PERSON>ey"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/ErrorProne"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/cliftonc"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/neyric"}, {"name": "humanchimp", "email": "<EMAIL>", "url": "https://github.com/humanchimp"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/spiceapps"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/danhbear"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>", "url": "https://github.com/baryshev"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/lfthomaz"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/greggzigler"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/jordanabderrachid"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "matsukaz"}], "dependencies": {"moment-timezone": "^0.5.x"}, "description": "Cron jobs for your node", "devDependencies": {"chai": "~4.2.x", "eslint": "~6.3.x", "eslint-config-prettier": "~6.2.x", "eslint-config-standard": "~14.1.x", "eslint-plugin-import": "~2.18.x", "eslint-plugin-node": "~10.0.x", "eslint-plugin-prettier": "~3.1.x", "eslint-plugin-promise": "~4.2.x", "eslint-plugin-standard": "~4.0.x", "mocha": "~6.2.x", "prettier": "~1.18.x", "sinon": "~7.4.x"}, "homepage": "https://github.com/kelektiv/node-cron#readme", "keywords": ["cron", "node cron", "node-cron", "schedule", "scheduler", "cronjob", "cron job"], "license": "MIT", "main": "lib/cron", "name": "cron", "repository": {"type": "git", "url": "git+ssh://**************/kelektiv/node-cron.git"}, "scripts": {"test": "make test"}, "version": "1.7.2"}