{"_args": [["amdefine@1.0.1", "/app"]], "_from": "amdefine@1.0.1", "_id": "amdefine@1.0.1", "_inBundle": false, "_integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "_location": "/amdefine", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "amdefine@1.0.1", "name": "amdefine", "escapedName": "amdefine", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/source-map"], "_resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "_spec": "1.0.1", "_where": "/app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/jrburke"}, "bugs": {"url": "https://github.com/jrburke/amdefine/issues"}, "description": "Provide AMD's define() API for declaring modules in the AMD format", "engines": {"node": ">=0.4.2"}, "homepage": "http://github.com/jrburke/amdefine", "license": "BSD-3-<PERSON><PERSON> OR MIT", "main": "./amdefine.js", "name": "amdefine", "repository": {"type": "git", "url": "git+https://github.com/jrburke/amdefine.git"}, "version": "1.0.1"}