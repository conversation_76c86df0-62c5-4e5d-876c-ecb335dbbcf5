{"_from": "log-timestamp", "_id": "log-timestamp@0.3.0", "_inBundle": false, "_integrity": "sha512-luRz6soxijd1aJh0GkLXFjKABihxthvTfWTzu3XhCgg5EivG2bsTpSd63QFbUgS+/KmFtL+0RfSpeaD2QvOV8Q==", "_location": "/log-timestamp", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "log-timestamp", "name": "log-timestamp", "escapedName": "log-timestamp", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/log-timestamp/-/log-timestamp-0.3.0.tgz", "_shasum": "2e10f1f0db872674ef3ecf53d6a312840acae45f", "_spec": "log-timestamp", "_where": "/app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.daveeddy.com"}, "bugs": {"url": "https://github.com/bahamas10/node-log-timestamp/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {"log-prefix": "0.1.1"}, "deprecated": false, "description": "Prepend timestamps to functions like console.log, console.warn, etc", "devDependencies": {}, "engines": {"node": "*"}, "homepage": "https://github.com/bahamas10/node-log-timestamp#readme", "keywords": ["console", "console.log", "log", "logger", "timestamp"], "main": "./log-timestamp.js", "name": "log-timestamp", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/bahamas10/node-log-timestamp.git"}, "scripts": {"test": "for f in tests/*; do echo \"$f\"; node \"$f\"; done; echo 'passed!'; exit 0"}, "version": "0.3.0"}