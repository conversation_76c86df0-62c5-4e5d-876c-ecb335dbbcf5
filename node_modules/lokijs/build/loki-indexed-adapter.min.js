(function(root,factory){if(typeof define==="function"&&define.amd){define([],factory)}else if(typeof exports==="object"){module.exports=factory()}else{root.LokiIndexedAdapter=factory()}})(this,function(){return function(){function LokiIndexedAdapter(appname,options){this.app="loki";this.options=options||{};if(typeof appname!=="undefined"){this.app=appname}this.catalog=null;if(!this.checkAvailability()){throw new Error("indexedDB does not seem to be supported for your environment")}}LokiIndexedAdapter.prototype.closeDatabase=function(){if(this.catalog&&this.catalog.db){this.catalog.db.close();this.catalog.db=null}};LokiIndexedAdapter.prototype.checkAvailability=function(){if(typeof indexedDB!=="undefined"&&indexedDB)return true;return false};LokiIndexedAdapter.prototype.loadDatabase=function(dbname,callback){var appName=this.app;var adapter=this;if(this.catalog===null||this.catalog.db===null){this.catalog=new LokiCatalog(function(cat){adapter.catalog=cat;adapter.loadDatabase(dbname,callback)});return}this.catalog.getAppKey(appName,dbname,function(result){if(typeof callback==="function"){if(result.id===0){callback(null);return}callback(result.val)}else{console.log(result.val)}})};LokiIndexedAdapter.prototype.loadKey=LokiIndexedAdapter.prototype.loadDatabase;LokiIndexedAdapter.prototype.saveDatabase=function(dbname,dbstring,callback){var appName=this.app;var adapter=this;function saveCallback(result){if(result&&result.success===true){callback(null)}else{callback(new Error("Error saving database"))}if(adapter.options.closeAfterSave){adapter.closeDatabase()}}if(this.catalog===null||this.catalog.db===null){this.catalog=new LokiCatalog(function(cat){adapter.saveDatabase(dbname,dbstring,saveCallback)});return}this.catalog.setAppKey(appName,dbname,dbstring,saveCallback)};LokiIndexedAdapter.prototype.saveKey=LokiIndexedAdapter.prototype.saveDatabase;LokiIndexedAdapter.prototype.deleteDatabase=function(dbname,callback){var appName=this.app;var adapter=this;if(this.catalog===null||this.catalog.db===null){this.catalog=new LokiCatalog(function(cat){adapter.catalog=cat;adapter.deleteDatabase(dbname,callback)});return}this.catalog.getAppKey(appName,dbname,function(result){var id=result.id;if(id!==0){adapter.catalog.deleteAppKey(id,callback)}else if(typeof callback==="function"){callback({success:true})}})};LokiIndexedAdapter.prototype.deleteKey=LokiIndexedAdapter.prototype.deleteDatabase;LokiIndexedAdapter.prototype.deleteDatabasePartitions=function(dbname){var self=this;this.getDatabaseList(function(result){result.forEach(function(str){if(str.startsWith(dbname)){self.deleteDatabase(str)}})})};LokiIndexedAdapter.prototype.getDatabaseList=function(callback){var appName=this.app;var adapter=this;if(this.catalog===null||this.catalog.db===null){this.catalog=new LokiCatalog(function(cat){adapter.catalog=cat;adapter.getDatabaseList(callback)});return}this.catalog.getAppKeys(appName,function(results){var names=[];for(var idx=0;idx<results.length;idx++){names.push(results[idx].key)}if(typeof callback==="function"){callback(names)}else{names.forEach(function(obj){console.log(obj)})}})};LokiIndexedAdapter.prototype.getKeyList=LokiIndexedAdapter.prototype.getDatabaseList;LokiIndexedAdapter.prototype.getCatalogSummary=function(callback){var appName=this.app;var adapter=this;if(this.catalog===null||this.catalog.db===null){this.catalog=new LokiCatalog(function(cat){adapter.catalog=cat;adapter.getCatalogSummary(callback)});return}this.catalog.getAllKeys(function(results){var entries=[];var obj,size,oapp,okey,oval;for(var idx=0;idx<results.length;idx++){obj=results[idx];oapp=obj.app||"";okey=obj.key||"";oval=obj.val||"";size=oapp.length*2+okey.length*2+oval.length+1;entries.push({app:obj.app,key:obj.key,size:size})}if(typeof callback==="function"){callback(entries)}else{entries.forEach(function(obj){console.log(obj)})}})};function LokiCatalog(callback){this.db=null;this.initializeLokiCatalog(callback)}LokiCatalog.prototype.initializeLokiCatalog=function(callback){var openRequest=indexedDB.open("LokiCatalog",1);var cat=this;openRequest.onupgradeneeded=function(e){var thisDB=e.target.result;if(thisDB.objectStoreNames.contains("LokiAKV")){thisDB.deleteObjectStore("LokiAKV")}if(!thisDB.objectStoreNames.contains("LokiAKV")){var objectStore=thisDB.createObjectStore("LokiAKV",{keyPath:"id",autoIncrement:true});objectStore.createIndex("app","app",{unique:false});objectStore.createIndex("key","key",{unique:false});objectStore.createIndex("appkey","appkey",{unique:true})}};openRequest.onsuccess=function(e){cat.db=e.target.result;if(typeof callback==="function")callback(cat)};openRequest.onerror=function(e){throw e}};LokiCatalog.prototype.getAppKey=function(app,key,callback){var transaction=this.db.transaction(["LokiAKV"],"readonly");var store=transaction.objectStore("LokiAKV");var index=store.index("appkey");var appkey=app+","+key;var request=index.get(appkey);request.onsuccess=function(usercallback){return function(e){var lres=e.target.result;if(lres===null||typeof lres==="undefined"){lres={id:0,success:false}}if(typeof usercallback==="function"){usercallback(lres)}else{console.log(lres)}}}(callback);request.onerror=function(usercallback){return function(e){if(typeof usercallback==="function"){usercallback({id:0,success:false})}else{throw e}}}(callback)};LokiCatalog.prototype.getAppKeyById=function(id,callback,data){var transaction=this.db.transaction(["LokiAKV"],"readonly");var store=transaction.objectStore("LokiAKV");var request=store.get(id);request.onsuccess=function(data,usercallback){return function(e){if(typeof usercallback==="function"){usercallback(e.target.result,data)}else{console.log(e.target.result)}}}(data,callback)};LokiCatalog.prototype.setAppKey=function(app,key,val,callback){var transaction=this.db.transaction(["LokiAKV"],"readwrite");var store=transaction.objectStore("LokiAKV");var index=store.index("appkey");var appkey=app+","+key;var request=index.get(appkey);request.onsuccess=function(e){var res=e.target.result;if(res===null||res===undefined){res={app:app,key:key,appkey:app+","+key,val:val}}else{res.val=val}var requestPut=store.put(res);requestPut.onerror=function(usercallback){return function(e){if(typeof usercallback==="function"){usercallback({success:false})}else{console.error("LokiCatalog.setAppKey (set) onerror");console.error(request.error)}}}(callback);requestPut.onsuccess=function(usercallback){return function(e){if(typeof usercallback==="function"){usercallback({success:true})}}}(callback)};request.onerror=function(usercallback){return function(e){if(typeof usercallback==="function"){usercallback({success:false})}else{console.error("LokiCatalog.setAppKey (get) onerror");console.error(request.error)}}}(callback)};LokiCatalog.prototype.deleteAppKey=function(id,callback){var transaction=this.db.transaction(["LokiAKV"],"readwrite");var store=transaction.objectStore("LokiAKV");var request=store.delete(id);request.onsuccess=function(usercallback){return function(evt){if(typeof usercallback==="function")usercallback({success:true})}}(callback);request.onerror=function(usercallback){return function(evt){if(typeof usercallback==="function"){usercallback({success:false})}else{console.error("LokiCatalog.deleteAppKey raised onerror");console.error(request.error)}}}(callback)};LokiCatalog.prototype.getAppKeys=function(app,callback){var transaction=this.db.transaction(["LokiAKV"],"readonly");var store=transaction.objectStore("LokiAKV");var index=store.index("app");var singleKeyRange=IDBKeyRange.only(app);var cursor=index.openCursor(singleKeyRange);var localdata=[];cursor.onsuccess=function(data,callback){return function(e){var cursor=e.target.result;if(cursor){var currObject=cursor.value;data.push(currObject);cursor.continue()}else{if(typeof callback==="function"){callback(data)}else{console.log(data)}}}}(localdata,callback);cursor.onerror=function(usercallback){return function(e){if(typeof usercallback==="function"){usercallback(null)}else{console.error("LokiCatalog.getAppKeys raised onerror");console.error(e)}}}(callback)};LokiCatalog.prototype.getAllKeys=function(callback){var transaction=this.db.transaction(["LokiAKV"],"readonly");var store=transaction.objectStore("LokiAKV");var cursor=store.openCursor();var localdata=[];cursor.onsuccess=function(data,callback){return function(e){var cursor=e.target.result;if(cursor){var currObject=cursor.value;data.push(currObject);cursor.continue()}else{if(typeof callback==="function"){callback(data)}else{console.log(data)}}}}(localdata,callback);cursor.onerror=function(usercallback){return function(e){if(typeof usercallback==="function")usercallback(null)}}(callback)};return LokiIndexedAdapter}()});
