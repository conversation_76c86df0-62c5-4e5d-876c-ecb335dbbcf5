{"_args": [["is-nan@1.3.0", "/app"]], "_from": "is-nan@1.3.0", "_id": "is-nan@1.3.0", "_inBundle": false, "_integrity": "sha512-z7bbREymOqt2CCaZVly8aC4ML3Xhfi0ekuOnjO2L8vKdl+CttdVoGZQhd4adMFAsxQ5VeRVwORs4tU8RH+HFtQ==", "_location": "/is-nan", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-nan@1.3.0", "name": "is-nan", "escapedName": "is-nan", "rawSpec": "1.3.0", "saveSpec": null, "fetchSpec": "1.3.0"}, "_requiredBy": ["/cron-parser"], "_resolved": "https://registry.npmjs.org/is-nan/-/is-nan-1.3.0.tgz", "_spec": "1.3.0", "_where": "/app", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/es-shims/is-nan/issues"}, "dependencies": {"define-properties": "^1.1.3"}, "description": "ES2015-compliant shim for Number.isNaN - the global isNaN returns false positives.", "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^15.0.2", "covert": "^1.1.1", "es5-shim": "^4.5.13", "eslint": "^6.7.2", "functions-have-names": "^1.2.0", "tape": "^4.11.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/es-shims/is-nan", "keywords": ["is", "NaN", "not a number", "number", "isNaN", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "license": "MIT", "main": "index.js", "name": "is-nan", "repository": {"type": "git", "url": "git://github.com/es-shims/is-nan.git"}, "scripts": {"coverage": "covert test/*.js", "lint": "eslint .", "posttest": "npx aud", "pretest": "npm run lint && es-shim-api", "test": "npm run tests-only", "test:function": "node test/index", "test:shimmed": "node test/shimmed", "tests-only": "npm run test:function && npm run --silent test:shimmed"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.3.0"}