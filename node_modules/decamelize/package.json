{"_args": [["decamelize@1.2.0", "/app"]], "_from": "decamelize@1.2.0", "_id": "decamelize@1.2.0", "_inBundle": false, "_integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "_location": "/decamelize", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "decamelize@1.2.0", "name": "decamelize", "escapedName": "decamelize", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "_spec": "1.2.0", "_where": "/app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/decamelize/issues"}, "description": "Convert a camelized string into a lowercased one with a custom separator: unicorn<PERSON>ain<PERSON> → unicorn_rainbow", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/decamelize#readme", "keywords": ["decamelize", "decamelcase", "camelcase", "lowercase", "case", "dash", "hyphen", "string", "str", "text", "convert"], "license": "MIT", "name": "decamelize", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decamelize.git"}, "scripts": {"test": "xo && ava"}, "version": "1.2.0"}