{"_args": [["cron-parser@2.13.0", "/app"]], "_from": "cron-parser@2.13.0", "_id": "cron-parser@2.13.0", "_inBundle": false, "_integrity": "sha512-UWeIpnRb0eyoWPVk+pD3TDpNx3KCFQeezO224oJIkktBrcW6RoAPOx5zIKprZGfk6vcYSmA8yQXItejSaDBhbQ==", "_location": "/cron-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cron-parser@2.13.0", "name": "cron-parser", "escapedName": "cron-parser", "rawSpec": "2.13.0", "saveSpec": null, "fetchSpec": "2.13.0"}, "_requiredBy": ["/node-schedule"], "_resolved": "https://registry.npmjs.org/cron-parser/-/cron-parser-2.13.0.tgz", "_spec": "2.13.0", "_where": "/app", "author": {"name": "<PERSON><PERSON>"}, "browser": {"fs": false}, "bugs": {"url": "https://github.com/harrisiirak/cron-parser/issues"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Renault John <PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Santiago Gimeno", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"is-nan": "^1.2.1", "moment-timezone": "^0.5.25"}, "description": "Node.js library for parsing crontab instructions", "devDependencies": {"sinon": "^7.3.2", "tap": "^14.2.2"}, "directories": {"test": "test"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/harrisiirak/cron-parser#readme", "keywords": ["cron", "crontab", "parser"], "license": "MIT", "main": "lib/parser.js", "name": "cron-parser", "repository": {"type": "git", "url": "git+https://github.com/harrisiirak/cron-parser.git"}, "scripts": {"test": "tap ./test/*.js"}, "types": "lib/index.d.ts", "version": "2.13.0"}