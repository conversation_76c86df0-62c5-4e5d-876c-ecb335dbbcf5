{"_args": [["debug@4.1.1", "/app"]], "_from": "debug@4.1.1", "_id": "debug@4.1.1", "_inBundle": false, "_integrity": "sha512-pYA<PERSON>zeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==", "_location": "/debug", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "debug@4.1.1", "name": "debug", "escapedName": "debug", "rawSpec": "4.1.1", "saveSpec": null, "fetchSpec": "4.1.1"}, "_requiredBy": ["/agenda"], "_resolved": "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz", "_spec": "4.1.1", "_where": "/app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "^2.1.1"}, "description": "small debugging utility", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "files": ["src", "dist/debug.js", "LICENSE", "README.md"], "homepage": "https://github.com/visionmedia/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "scripts": {"build": "npm run build:debug && npm run build:test", "build:debug": "babel -o dist/debug.js dist/debug.es6.js > dist/debug.js", "build:test": "babel -d dist test.js", "clean": "rimraf dist coverage", "lint": "xo", "prebuild:debug": "mkdir -p dist && browserify --standalone debug -o dist/debug.es6.js .", "pretest:browser": "npm run build", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls", "test:node": "istanbul cover _mocha -- test.js"}, "unpkg": "./dist/debug.js", "version": "4.1.1"}