{"_args": [["human-interval@1.0.0", "/app"]], "_from": "human-interval@1.0.0", "_id": "human-interval@1.0.0", "_inBundle": false, "_integrity": "sha512-SWPw3rD6/ocA0JnGePoXp5Zf5eILzsoL5vdWdLwtTuyrElyCpfQb0whIcxMdK/gAKNl2rFDGkPAbwI2KGZCvNA==", "_location": "/human-interval", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "human-interval@1.0.0", "name": "human-interval", "escapedName": "human-interval", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/agenda"], "_resolved": "https://registry.npmjs.org/human-interval/-/human-interval-1.0.0.tgz", "_spec": "1.0.0", "_where": "/app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://slingingcode.com/"}, "bugs": {"url": "https://github.com/agenda/human-interval/issues"}, "description": "Human readable time measurements", "devDependencies": {"ava": "^2.0.0", "xo": "^0.24.0"}, "homepage": "https://github.com/agenda/human-interval#readme", "keywords": ["interval", "time", "date"], "license": "MIT", "main": "index.js", "name": "human-interval", "repository": {"type": "git", "url": "git://github.com/agenda/human-interval.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0", "xo": {"space": 2, "rules": {"space-before-function-paren": ["error", "never"], "max-params": ["error", 5], "max-nested-callbacks": ["error", 5]}, "envs": ["node"]}}