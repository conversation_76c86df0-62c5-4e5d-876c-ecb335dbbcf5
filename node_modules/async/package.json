{"_args": [["async@0.2.10", "/app"]], "_from": "async@0.2.10", "_id": "async@0.2.10", "_inBundle": false, "_integrity": "sha1-trvgsGdLnXGXCMo43owjfLUmw9E=", "_location": "/async", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "async@0.2.10", "name": "async", "escapedName": "async", "rawSpec": "0.2.10", "saveSpec": null, "fetchSpec": "0.2.10"}, "_requiredBy": ["/uglify-js"], "_resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "_spec": "0.2.10", "_where": "/app", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "description": "Higher-order functions and common patterns for asynchronous code", "devDependencies": {"nodelint": ">0.0.0", "nodeunit": ">0.0.0", "uglify-js": "1.2.x"}, "homepage": "https://github.com/caolan/async#readme", "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"]}, "licenses": [{"type": "MIT", "url": "https://github.com/caolan/async/raw/master/LICENSE"}], "main": "./lib/async", "name": "async", "repository": {"type": "git", "url": "git+https://github.com/caolan/async.git"}, "scripts": {"test": "nodeunit test/test-async.js"}, "version": "0.2.10"}