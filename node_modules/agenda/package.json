{"_args": [["agenda@2.3.0", "/app"]], "_from": "agenda@2.3.0", "_id": "agenda@2.3.0", "_inBundle": false, "_integrity": "sha512-0RnozNC6Dy1lvaY3/BQt0IKlQrU5vSjBZ9fSG+5I31bNvU7VrzrZFNNuoBmsd99z5GwXzOIWVUkbddvj/VlfBg==", "_location": "/agenda", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "agenda@2.3.0", "name": "agenda", "escapedName": "agenda", "rawSpec": "2.3.0", "saveSpec": null, "fetchSpec": "2.3.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/agenda/-/agenda-2.3.0.tgz", "_spec": "2.3.0", "_where": "/app", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://slingingcode.com/"}, "bugs": {"url": "https://github.com/agenda/agenda/issues"}, "config": {"blanket": {"pattern": "lib", "data-cover-never": "node_modules"}}, "dependencies": {"cron": "~1.7.2", "date.js": "~0.3.3", "debug": "~4.1.1", "human-interval": "~1.0.0", "moment-timezone": "~0.5.27", "mongodb": "~3.4.0"}, "description": "Light weight job scheduler for Node.js", "devDependencies": {"blanket": "1.2.3", "coveralls": "3.0.9", "delay": "4.3.0", "eslint": "6.7.2", "eslint-config-xo": "0.27.2", "eslint-plugin-ava": "9.0.0", "eslint-plugin-eslint-comments": "3.1.2", "eslint-plugin-import": "2.19.1", "eslint-plugin-node": "10.0.0", "eslint-plugin-unicorn": "14.0.1", "expect.js": "0.3.1", "jsdoc": "3.6.3", "jsdoc-template": "git+https://github.com/braintree/jsdoc-template.git", "mocha": "6.2.2", "mocha-lcov-reporter": "1.3.0", "q": "1.5.1", "sinon": "7.5.0", "xo": "0.25.3"}, "engines": {"node": ">=8.0.0"}, "files": ["lib"], "homepage": "https://github.com/agenda/agenda#readme", "keywords": ["job", "jobs", "cron", "delayed", "scheduler", "runner"], "license": "MIT", "main": "index.js", "name": "agenda", "repository": {"type": "git", "url": "git://github.com/agenda/agenda.git"}, "scripts": {"docs": "jsdoc --configure .jsdoc.json --verbose", "lint": "xo --verbose", "mocha": "mocha --reporter spec --timeout 8000 -b", "mocha-debug": "DEBUG=agenda:**,-agenda:internal:** mocha --reporter spec --timeout 8000 -b", "mocha-debug-all": "DEBUG=agenda:** mocha --reporter spec --timeout 8000 -b", "mocha-debug-internal": "DEBUG=agenda:internal:** mocha --reporter spec --timeout 8000 -b", "test": "npm run lint && npm run mocha"}, "version": "2.3.0", "xo": {"space": 2, "ignores": ["docs/**"], "rules": {"space-before-function-paren": ["error", "never"], "max-params": ["error", 5], "max-nested-callbacks": ["error", 5]}, "envs": ["node"]}}