{"_args": [["date.js@0.3.3", "/app"]], "_from": "date.js@0.3.3", "_id": "date.js@0.3.3", "_inBundle": false, "_integrity": "sha512-HgigOS3h3k6HnW011nAb43c5xx5rBXk8P2v/WIT9Zv4koIaVXiH2BURguI78VVp+5Qc076T7OR378JViCnZtBw==", "_location": "/date.js", "_phantomChildren": {"ms": "2.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "date.js@0.3.3", "name": "date.js", "escapedName": "date.js", "rawSpec": "0.3.3", "saveSpec": null, "fetchSpec": "0.3.3"}, "_requiredBy": ["/agenda"], "_resolved": "https://registry.npmjs.org/date.js/-/date.js-0.3.3.tgz", "_spec": "0.3.3", "_where": "/app", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/MatthewMueller/date/issues"}, "dependencies": {"debug": "~3.1.0"}, "description": "dates for humans", "devDependencies": {"better-assert": "~1.0.0", "browserify": "^16.1.1", "mocha": "~4.0.1"}, "homepage": "https://github.com/MatthewMueller/date#readme", "keywords": ["nlp", "natural language processing", "time", "parser", "cfg", "dates", "humans", "parsing"], "license": "MIT", "main": "index.js", "name": "date.js", "repository": {"type": "git", "url": "git+https://github.com/MatthewMueller/date.git"}, "scripts": {"test": "make test"}, "spm": {"main": "index.js", "dependencies": {"debug": "0.8.1"}}, "version": "0.3.3"}