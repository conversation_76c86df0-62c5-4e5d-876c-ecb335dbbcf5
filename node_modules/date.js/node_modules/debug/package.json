{"_args": [["debug@3.1.0", "/app"]], "_from": "debug@3.1.0", "_id": "debug@3.1.0", "_inBundle": false, "_integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "_location": "/date.js/debug", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "debug@3.1.0", "name": "debug", "escapedName": "debug", "rawSpec": "3.1.0", "saveSpec": null, "fetchSpec": "3.1.0"}, "_requiredBy": ["/date.js"], "_resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "_spec": "3.1.0", "_where": "/app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "2.0.0"}, "description": "small debugging utility", "devDependencies": {"browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "homepage": "https://github.com/visionmedia/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "version": "3.1.0"}