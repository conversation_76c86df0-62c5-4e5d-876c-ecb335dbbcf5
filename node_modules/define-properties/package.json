{"_args": [["define-properties@1.1.3", "/app"]], "_from": "define-properties@1.1.3", "_id": "define-properties@1.1.3", "_inBundle": false, "_integrity": "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==", "_location": "/define-properties", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "define-properties@1.1.3", "name": "define-properties", "escapedName": "define-properties", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/is-nan"], "_resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz", "_spec": "1.1.3", "_where": "/app", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "dependencies": {"object-keys": "^1.0.12"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "devDependencies": {"@ljharb/eslint-config": "^13.0.0", "covert": "^1.1.0", "eslint": "^5.3.0", "jscs": "^3.0.7", "nsp": "^3.2.1", "tape": "^4.9.0"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/ljharb/define-properties#readme", "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "license": "MIT", "main": "index.js", "name": "define-properties", "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "scripts": {"coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "eslint": "eslint test/*.js *.js", "jscs": "jscs test/*.js *.js", "lint": "npm run --silent jscs && npm run --silent eslint", "posttest": "npm run --silent security", "pretest": "npm run --silent lint", "security": "nsp check", "test": "npm run --silent tests-only", "tests-only": "node test/index.js"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.3"}