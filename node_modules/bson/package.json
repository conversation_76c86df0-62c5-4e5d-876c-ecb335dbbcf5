{"_args": [["bson@1.1.3", "/app"]], "_from": "bson@1.1.3", "_id": "bson@1.1.3", "_inBundle": false, "_integrity": "sha512-TdiJxMVnodVS7r0BdL42y/pqC9cL2iKynVwA0Ho3qbsQYr428veL3l7BQyuqiw+Q5SqqoT0m4srSY/BlZ9AxXg==", "_location": "/bson", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bson@1.1.3", "name": "bson", "escapedName": "bson", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/mongodb"], "_resolved": "https://registry.npmjs.org/bson/-/bson-1.1.3.tgz", "_spec": "1.1.3", "_where": "/app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "lib/bson/bson.js", "bugs": {"url": "https://github.com/mongodb/js-bson/issues"}, "config": {"native": false}, "contributors": [], "description": "A bson parser for node.js and the browser", "devDependencies": {"babel-core": "^6.14.0", "babel-loader": "^6.2.5", "babel-polyfill": "^6.13.0", "babel-preset-es2015": "^6.14.0", "babel-preset-stage-0": "^6.5.0", "babel-register": "^6.14.0", "benchmark": "1.0.0", "colors": "1.1.0", "conventional-changelog-cli": "^1.3.5", "nodeunit": "0.9.0", "webpack": "^1.13.2", "webpack-polyfills-plugin": "0.0.9"}, "directories": {"lib": "./lib/bson"}, "engines": {"node": ">=0.6.19"}, "files": ["lib", "index.js", "browser_build", "bower.json"], "homepage": "https://github.com/mongodb/js-bson#readme", "keywords": ["mongodb", "bson", "parser"], "license": "Apache-2.0", "main": "./index", "name": "bson", "repository": {"type": "git", "url": "git+https://github.com/mongodb/js-bson.git"}, "scripts": {"build": "webpack --config ./webpack.dist.config.js", "changelog": "conventional-changelog -p angular -i HISTORY.md -s", "format": "prettier --print-width 100 --tab-width 2 --single-quote --write 'test/**/*.js' 'lib/**/*.js'", "lint": "eslint lib test", "test": "nodeunit ./test/node"}, "version": "1.1.3"}