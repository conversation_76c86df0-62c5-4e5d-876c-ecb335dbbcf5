{"_from": "log-prefix@0.1.1", "_id": "log-prefix@0.1.1", "_inBundle": false, "_integrity": "sha512-aP1Lst8OCdZKATqzXDN0JBissNVZuiKLyo6hOXDBxaQ1jHDsaxh2J1i5Pp0zMy6ayTKDWfUlLMXyLaQe1PJ48g==", "_location": "/log-prefix", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "log-prefix@0.1.1", "name": "log-prefix", "escapedName": "log-prefix", "rawSpec": "0.1.1", "saveSpec": null, "fetchSpec": "0.1.1"}, "_requiredBy": ["/log-timestamp"], "_resolved": "https://registry.npmjs.org/log-prefix/-/log-prefix-0.1.1.tgz", "_shasum": "3ec492138c8044c9f9732298492dca87850cac90", "_spec": "log-prefix@0.1.1", "_where": "/app/node_modules/log-timestamp", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.daveeddy.com"}, "bugs": {"url": "https://github.com/bahamas10/node-log-prefix/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {}, "deprecated": false, "description": "Prefix calls to console.log, console.warn, etc with whatever you'd like", "devDependencies": {}, "engines": {"node": "*"}, "homepage": "https://github.com/bahamas10/node-log-prefix#readme", "keywords": ["console", "console.log", "log", "logger", "timestamp", "prefix"], "main": "./log-prefix.js", "name": "log-prefix", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/bahamas10/node-log-prefix.git"}, "scripts": {"test": "for f in tests/*; do echo \"$f\"; node \"$f\"; done; echo 'passed!'; exit 0"}, "version": "0.1.1"}