{"_args": [["camelcase@1.2.1", "/app"]], "_from": "camelcase@1.2.1", "_id": "camelcase@1.2.1", "_inBundle": false, "_integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=", "_location": "/camelcase", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "camelcase@1.2.1", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "_spec": "1.2.1", "_where": "/app", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "node test.js"}, "version": "1.2.1"}