0 info it worked if it ends with ok
1 warn npm npm does not support Node.js v10.24.0
2 warn npm You should probably upgrade to a newer version of node as we
3 warn npm can't make any promises that npm will work with this version.
4 warn npm Supported releases of Node.js are the latest release of 4, 6, 7, 8, 9.
5 warn npm You can find the latest version at https://nodejs.org/
6 verbose cli [ '/usr/bin/node', '/usr/bin/npm', 'run', 'server.js' ]
7 info using npm@5.8.0
8 info using node@v10.24.0
9 verbose config Skipping project config: /app/.npmrc. (matches userconfig)
10 verbose stack Error: missing script: server.js
10 verbose stack     at run (/usr/share/npm/lib/run-script.js:151:19)
10 verbose stack     at /usr/share/npm/lib/run-script.js:61:5
10 verbose stack     at /usr/lib/nodejs/read-package-json/read-json.js:115:5
10 verbose stack     at /usr/lib/nodejs/read-package-json/read-json.js:418:5
10 verbose stack     at checkBinReferences_ (/usr/lib/nodejs/read-package-json/read-json.js:373:45)
10 verbose stack     at final (/usr/lib/nodejs/read-package-json/read-json.js:416:3)
10 verbose stack     at then (/usr/lib/nodejs/read-package-json/read-json.js:160:5)
10 verbose stack     at /usr/lib/nodejs/read-package-json/read-json.js:364:12
10 verbose stack     at /usr/lib/nodejs/graceful-fs/graceful-fs.js:78:16
10 verbose stack     at FSReqWrap.readFileAfterClose [as oncomplete] (internal/fs/read_file_context.js:53:3)
11 verbose cwd /app
12 verbose Linux 4.19.0-27-amd64
13 verbose argv "/usr/bin/node" "/usr/bin/npm" "run" "server.js"
14 verbose node v10.24.0
15 verbose npm  v5.8.0
16 error missing script: server.js
17 verbose exit [ 1, true ]
